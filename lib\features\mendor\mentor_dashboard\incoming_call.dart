// // incoming_call.dart

// import 'dart:async';
// import 'dart:convert';

// import 'package:flutter/material.dart';
// import 'package:http/http.dart' as http;
// import 'package:livekit_client/livekit_client.dart';
// import 'package:permission_handler/permission_handler.dart';
// import '../../../core/config/app_config.dart';
// import '../../../core/utils/logger.dart';

// class IncomingCall extends StatefulWidget {
//   final String mentorId;
//   final Map<String, dynamic>? incomingCall;
//   final bool showVideoCall;
//   final Function(String, Map<String, dynamic>?) onCallStatusChange;

//   const IncomingCall({
//     super.key,
//     required this.mentorId,
//     this.incomingCall,
//     required this.showVideoCall,
//     required this.onCallStatusChange,
//   });

//   @override
//   State<IncomingCall> createState() => _IncomingCallState();
// }

// class _IncomingCallState extends State<IncomingCall> {
//   final String apiBaseUrl = AppConfig.baseUrl;
//   final String livekitUrl = 'wss://livekit.sasthra.in';

//   String callState = 'idle';
//   Room? room;
//   List<LocalTrackPublication> localTracks = [];
//   bool isMuted = false;
//   bool isVideoOff = false;
//   bool isScreenSharing = false;
//   int callDuration = 0;
//   String error = '';
//   String mediaError = '';
//   Timer? callTimer;
//   LocalVideoTrack? localVideoTrack;
//   LocalAudioTrack? localAudioTrack;
//   RemoteVideoTrack? remoteVideoTrack;
//   RemoteAudioTrack? remoteAudioTrack;
//   RemoteVideoTrack? remoteScreenTrack;
//   RemoteAudioTrack? remoteScreenAudioTrack;
//   bool isRemoteAudioMuted = false;
//   RemoteParticipant? remoteParticipant;

//   @override
//   void initState() {
//     super.initState();
//     if (widget.incomingCall != null && widget.incomingCall!['token'] != null) {
//       _requestPermissions().then((_) {
//         _joinRoom(widget.incomingCall!['token']);
//       }).catchError((e) {
//         setState(() {
//           error = 'Permission error: $e';
//           callState = 'error';
//         });
//         AppLogger.error('Permission request failed: $e');
//       });
//     }
//   }

//   Future<void> _requestPermissions() async {
//     try {
//       var cameraStatus = await Permission.camera.status;
//       if (!cameraStatus.isGranted) {
//         cameraStatus = await Permission.camera.request();
//         if (!cameraStatus.isGranted) {
//           throw 'Camera permission denied';
//         }
//       }

//       var micStatus = await Permission.microphone.status;
//       if (!micStatus.isGranted) {
//         micStatus = await Permission.microphone.request();
//         if (!micStatus.isGranted) {
//           throw 'Microphone permission denied';
//         }
//       }

//       AppLogger.info('Permissions granted successfully');
//     } catch (e) {
//       AppLogger.error('Permission request error: $e');
//       rethrow;
//     }
//   }

//   void _startCallTimer() {
//     callTimer = Timer.periodic(const Duration(seconds: 1), (_) {
//       if (mounted) {
//         setState(() => callDuration++);
//       }
//     });
//   }

//   void _stopCallTimer() {
//     callTimer?.cancel();
//   }

//   String _formatDuration(int seconds) {
//     final mins = seconds ~/ 60;
//     final secs = seconds % 60;
//     return '${mins.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
//   }

//   Future<void> _joinRoom(String token) async {
//     if (!mounted) {
//       AppLogger.warning('Cannot join room: widget not mounted');
//       return;
//     }

//     if (token.isEmpty) {
//       AppLogger.error('Cannot join room: empty token');
//       setState(() {
//         error = 'Invalid token provided';
//         callState = 'error';
//       });
//       widget.onCallStatusChange('error', widget.incomingCall);
//       return;
//     }

//     setState(() => callState = 'connecting');
//     AppLogger.info('Attempting to join LiveKit room with URL: $livekitUrl');

//     try {
//       // Cleanup any existing room
//       _cleanup();

//       // Create room instance with error handling
//       room = Room();
//       if (room == null) {
//         throw Exception('Failed to create Room instance');
//       }

//       // Set up room listeners with null checks
//       room!.addListener(_onRoomEvent);

//       // Connect to room with timeout
//       await room!.connect(
//         livekitUrl,
//         token,
//         roomOptions: const RoomOptions(
//           adaptiveStream: true,
//           dynacast: true,
//           defaultCameraCaptureOptions: CameraCaptureOptions(
//             maxFrameRate: 24,
//             params: VideoParametersPresets.h540_169,
//           ),
//           defaultScreenShareCaptureOptions: ScreenShareCaptureOptions(
//             useiOSBroadcastExtension: true,
//             params: VideoParametersPresets.screenShareH720FPS15,
//           ),
//         ),
//       ).timeout(
//         const Duration(seconds: 15),
//         onTimeout: () => throw TimeoutException('Room connection timed out'),
//       );

//       if (!mounted || room == null) return;

//       AppLogger.info('Connected to LiveKit room successfully');

//       // Wait a moment for the room to stabilize
//       await Future.delayed(const Duration(milliseconds: 500));

//       if (!mounted || room == null) return;

//       // Enable camera and microphone with comprehensive error handling
//       await _enableMediaTracks();

//       // Get local tracks after enabling
//       await _updateLocalTracks();

//       if (mounted) {
//         setState(() => callState = 'connected');
//         _startCallTimer();
//         widget.onCallStatusChange('connected', widget.incomingCall);
//       }

//       AppLogger.info('Successfully joined LiveKit room and enabled media');

//     } on TimeoutException catch (e) {
//       AppLogger.error('Room connection timeout: $e');
//       if (mounted) {
//         setState(() {
//           error = 'Connection timed out. Please check your internet connection.';
//           callState = 'error';
//         });
//         widget.onCallStatusChange('error', widget.incomingCall);
//       }
//     } catch (e) {
//       AppLogger.error('Failed to join room: $e');
//       if (mounted) {
//         setState(() {
//           error = 'Failed to connect: ${e.toString()}';
//           callState = 'error';
//         });
//         widget.onCallStatusChange('error', widget.incomingCall);
//       }
//     }
//   }

//   Future<void> _enableMediaTracks() async {
//     if (room?.localParticipant == null) return;

//     try {
//       // Enable camera
//       await room!.localParticipant!.setCameraEnabled(true);
//       AppLogger.info('Camera enabled successfully');
//     } catch (camError) {
//       AppLogger.error('Failed to enable camera: $camError');
//       if (mounted) {
//         setState(() => mediaError += 'Camera: ${camError.toString()}\n');
//       }
//     }

//     try {
//       // Enable microphone
//       await room!.localParticipant!.setMicrophoneEnabled(true);
//       AppLogger.info('Microphone enabled successfully');
//     } catch (micError) {
//       AppLogger.error('Failed to enable microphone: $micError');
//       if (mounted) {
//         setState(() => mediaError += 'Microphone: ${micError.toString()}\n');
//       }
//     }
//   }

//   Future<void> _updateLocalTracks() async {
//     if (room?.localParticipant == null) return;

//     try {
//       // Get local video track
//       final videoPublications = room!.localParticipant!.videoTrackPublications
//           .where((pub) => pub.source == TrackSource.camera)
//           .toList();

//       if (videoPublications.isNotEmpty) {
//         final track = videoPublications.first.track;
//         if (track is LocalVideoTrack) {
//           localVideoTrack = track;
//           AppLogger.info('Local video track obtained');
//         }
//       }

//       // Get local audio track
//       final audioPublications = room!.localParticipant!.audioTrackPublications
//           .where((pub) => pub.source == TrackSource.microphone)
//           .toList();

//       if (audioPublications.isNotEmpty) {
//         final track = audioPublications.first.track;
//         if (track is LocalAudioTrack) {
//           localAudioTrack = track;
//           AppLogger.info('Local audio track obtained');
//         }
//       }

//       if (mounted) {
//         setState(() {});
//       }
//     } catch (e) {
//       AppLogger.error('Error updating local tracks: $e');
//     }
//   }

//   void _onRoomEvent() {
//     if (room == null || !mounted) return;

//     try {
//       // Handle remote participants
//       final participants = room!.remoteParticipants.values.toList();
//       if (participants.isNotEmpty) {
//         remoteParticipant = participants.first;
//         _updateRemoteTracks(remoteParticipant!);
//       } else {
//         // Clear remote tracks if no participants
//         remoteVideoTrack = null;
//         remoteAudioTrack = null;
//         remoteScreenTrack = null;
//         remoteParticipant = null;
//       }

//       if (mounted) {
//         setState(() {});
//       }
//     } catch (e) {
//       AppLogger.error('Error in room event handler: $e');
//     }
//   }

//   void _updateRemoteTracks(RemoteParticipant participant) {
//     try {
//       // Get remote video track
//       final videoPublications = participant.videoTrackPublications
//           .where((pub) => pub.source == TrackSource.camera)
//           .toList();

//       if (videoPublications.isNotEmpty) {
//         final track = videoPublications.first.track;
//         if (track is RemoteVideoTrack) {
//           remoteVideoTrack = track;
//           AppLogger.info('Remote video track updated');
//         }
//       }

//       // Get remote audio track
//       final audioPublications = participant.audioTrackPublications
//           .where((pub) => pub.source == TrackSource.microphone)
//           .toList();

//       if (audioPublications.isNotEmpty) {
//         final track = audioPublications.first.track;
//         if (track is RemoteAudioTrack) {
//           remoteAudioTrack = track;
//           AppLogger.info('Remote audio track updated');
//         }
//       }

//       // Get screen share track
//       final screenPublications = participant.videoTrackPublications
//           .where((pub) => pub.source == TrackSource.screenShareVideo)
//           .toList();

//       if (screenPublications.isNotEmpty) {
//         final track = screenPublications.first.track;
//         if (track is RemoteVideoTrack) {
//           remoteScreenTrack = track;
//           AppLogger.info('Remote screen share track updated');
//         }
//       }
//     } catch (e) {
//       AppLogger.error('Error updating remote tracks: $e');
//     }
//   }

//   Future<void> _endCall() async {
//     AppLogger.info('Ending call...');

//     try {
//       // Stop call timer first
//       _stopCallTimer();

//       // Notify API about call end
//       if (widget.incomingCall?['room_name'] != null) {
//         await http.post(
//           Uri.parse('$apiBaseUrl/call/end'),
//           headers: {
//             'Content-Type': 'application/json',
//             'Authorization': 'Bearer ${widget.incomingCall?['token'] ?? ''}',
//           },
//           body: jsonEncode({
//             'room_name': widget.incomingCall!['room_name'],
//             'mentor_id': widget.mentorId,
//             'action': 'end',
//           }),
//         ).timeout(
//           const Duration(seconds: 5),
//           onTimeout: () => throw TimeoutException('End call API timeout'),
//         );
//         AppLogger.info('Call ended via API successfully');
//       }
//     } catch (e) {
//       AppLogger.error('Error ending call via API: $e');
//       // Continue with cleanup even if API call fails
//     }

//     // Cleanup resources
//     _cleanup();

//     // Update UI state
//     if (mounted) {
//       setState(() => callState = 'ended');
//       widget.onCallStatusChange('ended', widget.incomingCall);

//       // Navigate back after a short delay
//       Future.delayed(const Duration(milliseconds: 500), () {
//         if (mounted) {
//           Navigator.of(context).pop();
//         }
//       });
//     }
//   }

//   void _cleanup() {
//     room?.disconnect();
//     room = null;
//     localTracks.clear();
//     localVideoTrack = null;
//     localAudioTrack = null;
//     remoteVideoTrack = null;
//     remoteAudioTrack = null;
//     remoteScreenTrack = null;
//     remoteScreenAudioTrack = null;
//     remoteParticipant = null;
//     _stopCallTimer();
//   }

//   Future<void> _toggleMute() async {
//     if (room?.localParticipant != null) {
//       try {
//         await room!.localParticipant!.setMicrophoneEnabled(!isMuted);
//         if (mounted) {
//           setState(() => isMuted = !isMuted);
//         }
//         AppLogger.info('Microphone ${isMuted ? 'muted' : 'unmuted'}');
//       } catch (e) {
//         AppLogger.error('Error toggling mute: $e');
//       }
//     }
//   }

//   Future<void> _toggleVideo() async {
//     if (room?.localParticipant != null) {
//       try {
//         await room!.localParticipant!.setCameraEnabled(!isVideoOff);
//         if (mounted) {
//           setState(() => isVideoOff = !isVideoOff);
//         }
//         AppLogger.info('Camera ${isVideoOff ? 'disabled' : 'enabled'}');
//       } catch (e) {
//         AppLogger.error('Error toggling video: $e');
//       }
//     }
//   }

//   Future<void> _toggleScreenShare() async {
//     if (room?.localParticipant == null) return;

//     try {
//       if (isScreenSharing) {
//         await room!.localParticipant!.setScreenShareEnabled(false);
//       } else {
//         await room!.localParticipant!.setScreenShareEnabled(true);
//       }
//       if (mounted) {
//         setState(() => isScreenSharing = !isScreenSharing);
//       }
//       AppLogger.info('Screen share ${isScreenSharing ? 'started' : 'stopped'}');
//     } catch (e) {
//       AppLogger.error('Error toggling screen share: $e');
//     }
//   }

//   @override
//   void dispose() {
//     _cleanup();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     if (!widget.showVideoCall || widget.incomingCall == null) return const SizedBox.shrink();

//     if (callState == 'connecting') {
//       return const Scaffold(body: Center(child: CircularProgressIndicator()));
//     }

//     if (callState == 'error') {
//       return Scaffold(body: Center(child: Text(error)));
//     }

//     if (callState == 'ended') {
//       return const Scaffold(body: Center(child: Text('Call Ended')));
//     }

//     if (callState == 'connected') {
//       return Scaffold(
//         body: Column(
//           children: [
//             // Header with duration, etc.
//             Container(
//               color: Colors.grey[800],
//               padding: const EdgeInsets.all(16),
//               child: Column(
//                 children: [
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       Row(
//                         children: [
//                           Container(
//                             width: 12,
//                             height: 12,
//                             decoration: const BoxDecoration(
//                               color: Colors.green,
//                               shape: BoxShape.circle,
//                             ),
//                           ),
//                           const SizedBox(width: 8),
//                           Text(
//                             'Connected | Student: ${widget.incomingCall!['student_id']}',
//                             style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
//                           ),
//                         ],
//                       ),
//                       Container(
//                         padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
//                         decoration: BoxDecoration(
//                           color: Colors.blue[600],
//                           borderRadius: BorderRadius.circular(12),
//                         ),
//                         child: Text(
//                           'Duration: ${_formatDuration(callDuration)}',
//                           style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
//                         ),
//                       ),
//                     ],
//                   ),
//                   if (remoteParticipant != null)
//                     Padding(
//                       padding: const EdgeInsets.only(top: 8),
//                       child: Text(
//                         'Participants: ${room?.remoteParticipants.length ?? 0 + 1}',
//                         style: TextStyle(color: Colors.grey[300], fontSize: 12),
//                       ),
//                     ),
//                 ],
//               ),
//             ),
//             if (mediaError.isNotEmpty)
//               Padding(
//                 padding: const EdgeInsets.all(8.0),
//                 child: Text(mediaError, style: const TextStyle(color: Colors.red)),
//               ),
//             Expanded(
//               child: Row(
//                 children: [
//                   // Remote Video
//                   Expanded(
//                     child: remoteVideoTrack != null
//                         ? Container(
//                             decoration: BoxDecoration(
//                               border: Border.all(color: Colors.blue, width: 2),
//                             ),
//                             child: VideoTrackRenderer(remoteVideoTrack!),
//                           )
//                         : Container(
//                             color: Colors.black,
//                             child: const Center(
//                               child: Column(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 children: [
//                                   Icon(Icons.person, color: Colors.white, size: 48),
//                                   SizedBox(height: 8),
//                                   Text('Waiting for remote video...', style: TextStyle(color: Colors.white)),
//                                 ],
//                               ),
//                             ),
//                           ),
//                   ),
//                   // Local Video
//                   Expanded(
//                     child: localVideoTrack != null
//                         ? Container(
//                             decoration: BoxDecoration(
//                               border: Border.all(color: Colors.green, width: 2),
//                             ),
//                             child: VideoTrackRenderer(localVideoTrack!),
//                           )
//                         : Container(
//                             color: Colors.grey[800],
//                             child: const Center(
//                               child: Column(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 children: [
//                                   Icon(Icons.videocam_off, color: Colors.white, size: 48),
//                                   SizedBox(height: 8),
//                                   Text('Camera disabled', style: TextStyle(color: Colors.white)),
//                                 ],
//                               ),
//                             ),
//                           ),
//                   ),
//                 ],
//               ),
//             ),
//             if (isScreenSharing && remoteScreenTrack != null)
//               Expanded(child: VideoTrackRenderer(remoteScreenTrack!)),
//             // Controls
//             Container(
//               color: Colors.grey[800],
//               padding: const EdgeInsets.all(16),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                 children: [
//                   // Mute/Unmute button
//                   Container(
//                     decoration: BoxDecoration(
//                       color: isMuted ? Colors.red : Colors.green,
//                       borderRadius: BorderRadius.circular(8),
//                     ),
//                     child: IconButton(
//                       onPressed: _toggleMute,
//                       icon: Icon(
//                         isMuted ? Icons.mic_off : Icons.mic,
//                         color: Colors.white,
//                       ),
//                     ),
//                   ),
//                   // Video on/off button
//                   Container(
//                     decoration: BoxDecoration(
//                       color: isVideoOff ? Colors.red : Colors.green,
//                       borderRadius: BorderRadius.circular(8),
//                     ),
//                     child: IconButton(
//                       onPressed: _toggleVideo,
//                       icon: Icon(
//                         isVideoOff ? Icons.videocam_off : Icons.videocam,
//                         color: Colors.white,
//                       ),
//                     ),
//                   ),
//                   // Screen share button
//                   Container(
//                     decoration: BoxDecoration(
//                       color: isScreenSharing ? Colors.blue : Colors.grey[600],
//                       borderRadius: BorderRadius.circular(8),
//                     ),
//                     child: IconButton(
//                       onPressed: _toggleScreenShare,
//                       icon: Icon(
//                         isScreenSharing ? Icons.stop_screen_share : Icons.screen_share,
//                         color: Colors.white,
//                       ),
//                     ),
//                   ),
//                   // End call button
//                   Container(
//                     decoration: BoxDecoration(
//                       color: Colors.red,
//                       borderRadius: BorderRadius.circular(8),
//                     ),
//                     child: IconButton(
//                       onPressed: _endCall,
//                       icon: const Icon(Icons.call_end, color: Colors.white),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       );
//     }

//     return const Scaffold(body: Center(child: Text('Unknown state')));
//   }
// }



// incoming_call.dart

import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:livekit_client/livekit_client.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../core/config/app_config.dart';
import '../../../core/utils/logger.dart';

class IncomingCall extends StatefulWidget {
  final String mentorId;
  final Map<String, dynamic>? incomingCall;
  final bool showVideoCall;
  final Function(String, Map<String, dynamic>?) onCallStatusChange;

  const IncomingCall({
    super.key,
    required this.mentorId,
    this.incomingCall,
    required this.showVideoCall,
    required this.onCallStatusChange,
  });

  @override
  State<IncomingCall> createState() => _IncomingCallState();
}

class _IncomingCallState extends State<IncomingCall> {
  final String apiBaseUrl = AppConfig.baseUrl;
  final String livekitUrl = 'wss://livekit.sasthra.in';

  String callState = 'idle';
  Room? room;
  bool isMuted = false;
  bool isVideoOff = false;
  bool isScreenSharing = false;
  int callDuration = 0;
  String error = '';
  String mediaError = '';
  Timer? callTimer;
  EventsListener<RoomEvent>? roomListener;
  Participant? localParticipant;
  Participant? remoteParticipant;

  @override
  void initState() {
    super.initState();
    if (widget.incomingCall != null && widget.incomingCall!['token'] != null) {
      _initializeCall();
    }
  }

  Future<void> _initializeCall() async {
    try {
      await _requestPermissions();
      await _joinRoom(widget.incomingCall!['token']);
    } catch (e) {
      if (mounted) {
        setState(() {
          error = 'Initialization error: $e';
          callState = 'error';
        });
      }
      AppLogger.error('Initialization failed: $e');
      widget.onCallStatusChange('error', widget.incomingCall);
    }
  }

  Future<void> _requestPermissions() async {
    try {
      final cameraStatus = await Permission.camera.request();
      if (!cameraStatus.isGranted) {
        throw 'Camera permission denied';
      }

      final micStatus = await Permission.microphone.request();
      if (!micStatus.isGranted) {
        throw 'Microphone permission denied';
      }

      AppLogger.info('Permissions granted successfully');
    } catch (e) {
      AppLogger.error('Permission request error: $e');
      rethrow;
    }
  }

  void _startCallTimer() {
    callTimer?.cancel();
    callTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (mounted) {
        setState(() => callDuration++);
      }
    });
  }

  void _stopCallTimer() {
    callTimer?.cancel();
  }

  String _formatDuration(int seconds) {
    final mins = seconds ~/ 60;
    final secs = seconds % 60;
    return '${mins.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  Future<void> _joinRoom(String token) async {
    if (token.isEmpty) {
      setState(() {
        error = 'Invalid token provided';
        callState = 'error';
      });
      widget.onCallStatusChange('error', widget.incomingCall);
      return;
    }

    setState(() => callState = 'connecting');
    AppLogger.info('Attempting to join LiveKit room with URL: $livekitUrl');

    try {
      room = Room();
      roomListener = room!.createListener();

      await room!.connect(
        livekitUrl,
        token,
        roomOptions: const RoomOptions(
          adaptiveStream: true,
          dynacast: true,
          defaultCameraCaptureOptions: CameraCaptureOptions(
            maxFrameRate: 24,
            params: VideoParametersPresets.h540_169,
          ),
          defaultScreenShareCaptureOptions: ScreenShareCaptureOptions(
            useiOSBroadcastExtension: true,
            params: VideoParametersPresets.screenShareH720FPS15,
          ),
        ),
      );

      if (!mounted || room == null) return;
      AppLogger.info('Connected to LiveKit room successfully');

      setState(() {
        localParticipant = room!.localParticipant;
        if (room!.remoteParticipants.isNotEmpty) {
          remoteParticipant = room!.remoteParticipants.values.first;
        }
      });

      roomListener!
        ..on<RoomDisconnectedEvent>((_) => _handleRoomDisconnect())
        ..on<ParticipantConnectedEvent>(_handleParticipantConnected)
        ..on<ParticipantDisconnectedEvent>(_handleParticipantDisconnected)
        ..on<TrackSubscribedEvent>(_onTrackSubscribed)
        ..on<TrackUnsubscribedEvent>(_onTrackUnsubscribed);

      await room!.localParticipant?.setCameraEnabled(true);
      await room!.localParticipant?.setMicrophoneEnabled(true);

      if (mounted) {
        setState(() => callState = 'connected');
        _startCallTimer();
        widget.onCallStatusChange('connected', widget.incomingCall);
      }

    } catch (e) {
      AppLogger.error('Failed to join room: $e');
      if (mounted) {
        setState(() {
          error = 'Failed to connect: ${e.toString()}';
          callState = 'error';
        });
        widget.onCallStatusChange('error', widget.incomingCall);
      }
    }
  }

  void _handleRoomDisconnect() {
    if (mounted) {
      _endCall(shouldNotifyApi: false);
    }
  }

  void _handleParticipantConnected(ParticipantConnectedEvent event) {
    if (mounted) {
      setState(() => remoteParticipant = event.participant);
    }
  }

  void _handleParticipantDisconnected(ParticipantDisconnectedEvent event) {
    if (mounted) {
      setState(() => remoteParticipant = null);
    }
  }

  void _onTrackSubscribed(TrackSubscribedEvent event) {
    if (mounted) {
      setState(() {});
    }
  }

  void _onTrackUnsubscribed(TrackUnsubscribedEvent event) {
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _endCall({bool shouldNotifyApi = true}) async {
    AppLogger.info('Ending call...');

    _stopCallTimer();
    
    if (shouldNotifyApi && widget.incomingCall?['room_name'] != null) {
      try {
        await http.post(
          Uri.parse('$apiBaseUrl/call/end'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ${widget.incomingCall?['token'] ?? ''}',
          },
          body: jsonEncode({
            'room_name': widget.incomingCall!['room_name'],
            'mentor_id': widget.mentorId,
            'action': 'end',
          }),
        ).timeout(const Duration(seconds: 5));
        AppLogger.info('Call ended via API successfully');
      } catch (e) {
        AppLogger.error('Error ending call via API: $e');
      }
    }

    await _cleanup();

    if (mounted) {
      setState(() => callState = 'ended');
      widget.onCallStatusChange('ended', widget.incomingCall);
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> _cleanup() async {
    await room?.disconnect();
    await roomListener?.dispose();
    room = null;
    roomListener = null;
    _stopCallTimer();
  }

  Future<void> _toggleMute() async {
    if (room?.localParticipant != null) {
      final newMuteState = !isMuted;
      await room!.localParticipant!.setMicrophoneEnabled(!newMuteState);
      if (mounted) {
        setState(() => isMuted = newMuteState);
      }
    }
  }

  Future<void> _toggleVideo() async {
    if (room?.localParticipant != null) {
      final newVideoState = !isVideoOff;
      await room!.localParticipant!.setCameraEnabled(!newVideoState);
      if (mounted) {
        setState(() => isVideoOff = newVideoState);
      }
    }
  }

  Future<void> _toggleScreenShare() async {
    if (room?.localParticipant != null) {
      final newScreenShareState = !isScreenSharing;
      await room!.localParticipant!.setScreenShareEnabled(newScreenShareState);
      if (mounted) {
        setState(() => isScreenSharing = newScreenShareState);
      }
    }
  }

  @override
  void dispose() {
    _cleanup();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showVideoCall || widget.incomingCall == null) return const SizedBox.shrink();

    if (callState == 'connecting') {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (callState == 'error') {
      return Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(error, textAlign: TextAlign.center),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    if (Navigator.canPop(context)) {
                      Navigator.pop(context);
                    }
                  },
                  child: const Text('Go Back'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (callState == 'ended') {
      return const Scaffold(body: Center(child: Text('Call Ended')));
    }

    if (callState == 'connected') {
      return Scaffold(
        body: Column(
          children: [
            // Header
            SafeArea(
              child: Container(
                color: Colors.grey[800],
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Student: ${widget.incomingCall!['student_id']}',
                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'Duration: ${_formatDuration(callDuration)}',
                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
            ),
            if (mediaError.isNotEmpty)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(mediaError, style: const TextStyle(color: Colors.red)),
              ),
            // Video views
            Expanded(
              child: Stack(
                children: [
                  // Remote video
                  _buildParticipantView(remoteParticipant, isRemote: true),
                  // Local video
                  Positioned(
                    top: 16,
                    right: 16,
                    width: 120,
                    height: 160,
                    child: _buildParticipantView(localParticipant, isRemote: false),
                  ),
                ],
              ),
            ),
            // Controls
            Container(
              color: Colors.grey[800],
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildControlButton(
                    onPressed: _toggleMute,
                    icon: isMuted ? Icons.mic_off : Icons.mic,
                    backgroundColor: isMuted ? Colors.red : Colors.green,
                  ),
                  _buildControlButton(
                    onPressed: _toggleVideo,
                    icon: isVideoOff ? Icons.videocam_off : Icons.videocam,
                    backgroundColor: isVideoOff ? Colors.red : Colors.green,
                  ),
                  _buildControlButton(
                    onPressed: _toggleScreenShare,
                    icon: isScreenSharing ? Icons.stop_screen_share : Icons.screen_share,
                    backgroundColor: isScreenSharing ? Colors.blue : Colors.grey[600]!,
                  ),
                  _buildControlButton(
                    onPressed: () => _endCall(),
                    icon: Icons.call_end,
                    backgroundColor: Colors.red,
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
    return const Scaffold(body: Center(child: Text('Unknown state')));
  }

  Widget _buildParticipantView(Participant? participant, {required bool isRemote}) {
    if (participant == null) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Text(
            isRemote ? 'Waiting for student...' : 'No local video',
            style: const TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    final videoTrack = participant.videoTrackPublications.isNotEmpty
        ? participant.videoTrackPublications.first.track as VideoTrack?
        : null;

    if (videoTrack == null || videoTrack.muted) {
      return Container(
        color: Colors.grey[800],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(isRemote ? Icons.person : Icons.videocam_off, color: Colors.white, size: 48),
              const SizedBox(height: 8),
              Text(
                isRemote ? 'Student\'s camera is off' : 'Your camera is off',
                style: const TextStyle(color: Colors.white),
              ),
            ],
          ),
        ),
      );
    }

    return VideoTrackRenderer(videoTrack);
  }

  Widget _buildControlButton({required VoidCallback onPressed, required IconData icon, required Color backgroundColor}) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: Colors.white),
      ),
    );
  }
}