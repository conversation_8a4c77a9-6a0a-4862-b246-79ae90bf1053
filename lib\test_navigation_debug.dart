import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'core/utils/logger.dart';

void main() {
  runApp(NavigationTestApp());
}

class NavigationTestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Navigation Test',
      routerConfig: _router,
    );
  }
}

final _router = GoRouter(
  initialLocation: '/test',
  routes: [
    GoRoute(
      path: '/test',
      builder: (context, state) => NavigationTestPage(),
    ),
  ],
);

class NavigationTestPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Navigation Test')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Test Attendance Page Navigation',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: () {
                final url = '/dashboard/parent/student_attendance';
                print('🔍 Attempting to navigate to: $url');
                AppLogger.info('Testing navigation to: $url');
                try {
                  context.go(url);
                } catch (e) {
                  print('❌ Navigation failed: $e');
                  AppLogger.error('Navigation failed: $e');
                }
              },
              child: Text('Navigate to /dashboard/parent/student_attendance'),
            ),
            
            SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () {
                final url = '/dashboard/parent/student-attendance';
                print('🔍 Attempting to navigate to: $url');
                AppLogger.info('Testing navigation to: $url');
                try {
                  context.go(url);
                } catch (e) {
                  print('❌ Navigation failed: $e');
                  AppLogger.error('Navigation failed: $e');
                }
              },
              child: Text('Navigate to /dashboard/parent/student-attendance'),
            ),
            
            SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () {
                final url = '/dashboard/parent/attendance';
                print('🔍 Attempting to navigate to: $url');
                AppLogger.info('Testing navigation to: $url');
                try {
                  context.go(url);
                } catch (e) {
                  print('❌ Navigation failed: $e');
                  AppLogger.error('Navigation failed: $e');
                }
              },
              child: Text('Navigate to /dashboard/parent/attendance'),
            ),
            
            SizedBox(height: 32),
            
            Text(
              'Debug Information:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Current route: ${GoRouterState.of(context).uri.toString()}'),
            Text('Expected route: /dashboard/parent/student_attendance'),
            Text('Router should normalize: student_attendance -> student_attendance'),
            Text('Router should normalize: student-attendance -> student_attendance'),
          ],
        ),
      ),
    );
  }
}
