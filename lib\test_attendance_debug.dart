import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Debug widget to test attendance page navigation
class TestAttendanceDebug extends StatelessWidget {
  const TestAttendanceDebug({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Attendance Navigation'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Debug Attendance Navigation',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            const Text(
              'Test these URLs to see which one works:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () {
                print('Navigating to: /dashboard/parent/student_attendance');
                context.go('/dashboard/parent/student_attendance');
              },
              child: const Text('Test: /dashboard/parent/student_attendance'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(12),
              ),
            ),
            
            const SizedBox(height: 8),
            
            ElevatedButton(
              onPressed: () {
                print('Navigating to: /dashboard/parent/attendance');
                context.go('/dashboard/parent/attendance');
              },
              child: const Text('Test: /dashboard/parent/attendance'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(12),
              ),
            ),
            
            const SizedBox(height: 8),
            
            ElevatedButton(
              onPressed: () {
                print('Navigating to: /dashboard/parent/student-attendance');
                context.go('/dashboard/parent/student-attendance');
              },
              child: const Text('Test: /dashboard/parent/student-attendance'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(12),
              ),
            ),
            
            const SizedBox(height: 32),
            
            const Text(
              'Other Parent Pages:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: () {
                print('Navigating to: /dashboard/parent/student_info');
                context.go('/dashboard/parent/student_info');
              },
              child: const Text('Student Info'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(12),
              ),
            ),
            
            const SizedBox(height: 8),
            
            ElevatedButton(
              onPressed: () {
                print('Navigating to: /dashboard/parent/center_info');
                context.go('/dashboard/parent/center_info');
              },
              child: const Text('Center Info'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(12),
              ),
            ),
            
            const SizedBox(height: 32),
            
            const Text(
              'Instructions:',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Check the console for debug output\n'
              '2. Try each attendance URL to see which works\n'
              '3. Look for "DEBUG: Parent routing" messages\n'
              '4. The working URL should show "DEBUG: Returning AttendancePage"',
              style: TextStyle(fontSize: 12),
            ),
            
            const Spacer(),
            
            ElevatedButton(
              onPressed: () {
                context.go('/dashboard');
              },
              child: const Text('Back to Dashboard'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
